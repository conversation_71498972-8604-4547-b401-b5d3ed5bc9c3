package com.crentsil.demo.mapper;

import com.crentsil.demo.dto.CourseCreateDto;
import com.crentsil.demo.dto.CourseResponseDto;
import com.crentsil.demo.model.Course;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class CourseMapper {
    
    public Course toEntity(CourseCreateDto dto) {
        Course course = new Course();
        course.setCourseCode(dto.getCourseCode());
        course.setCourseName(dto.getCourseName());
        course.setDescription(dto.getDescription());
        course.setCredits(dto.getCredits());
        return course;
    }
    
    public CourseResponseDto toResponseDto(Course course) {
        CourseResponseDto dto = new CourseResponseDto();
        dto.setId(course.getId());
        dto.setCourseCode(course.getCourseCode());
        dto.setCourseName(course.getCourseName());
        dto.setDescription(course.getDescription());
        dto.setCredits(course.getCredits());
        return dto;
    }
    
    public List<CourseResponseDto> toResponseDtoList(List<Course> courses) {
        return courses.stream()
                .map(this::toResponseDto)
                .collect(Collectors.toList());
    }
}
