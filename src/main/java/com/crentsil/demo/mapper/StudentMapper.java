package com.crentsil.demo.mapper;

import com.crentsil.demo.dto.StudentCreateDto;
import com.crentsil.demo.dto.StudentResponseDto;
import com.crentsil.demo.dto.StudentUpdateDto;
import com.crentsil.demo.model.Student;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class StudentMapper {
    
    @Autowired
    private CourseMapper courseMapper;
    
    public Student toEntity(StudentCreateDto dto) {
        Student student = new Student();
        student.setName(dto.getName());
        student.setAge(dto.getAge());
        student.setDob(dto.getDob());
        student.setPassword(dto.getPassword());
        return student;
    }
    
    public StudentResponseDto toResponseDto(Student student) {
        StudentResponseDto dto = new StudentResponseDto();
        dto.setId(student.getId());
        dto.setName(student.getName());
        dto.setAge(student.getAge());
        dto.setDob(student.getDob());
        // Convert courses to DTOs if they exist
        if (student.getCourses() != null && !student.getCourses().isEmpty()) {
            dto.setCourses(courseMapper.toResponseDtoList(student.getCourses().stream().toList()));
        }
        return dto;
    }
    
    public void updateEntityFromDto(Student student, StudentUpdateDto dto) {
        if (dto.getName() != null) {
            student.setName(dto.getName());
        }
        if (dto.getAge() != null) {
            student.setAge(dto.getAge());
        }
        if (dto.getDob() != null) {
            student.setDob(dto.getDob());
        }
        if (dto.getPassword() != null) {
            student.setPassword(dto.getPassword());
        }
    }
    
    public List<StudentResponseDto> toResponseDtoList(List<Student> students) {
        return students.stream()
                .map(this::toResponseDto)
                .collect(Collectors.toList());
    }
}
