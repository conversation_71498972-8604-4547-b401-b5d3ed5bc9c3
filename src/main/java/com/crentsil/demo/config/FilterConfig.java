package com.crentsil.demo.config;

import com.crentsil.demo.middleware.BasicAuth;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<BasicAuth> basicAuthFilter(BasicAuth basicAuth) {
        FilterRegistrationBean<BasicAuth> registrationBean = new FilterRegistrationBean<>();
        
        registrationBean.setFilter(basicAuth);
        
        // Apply to all URLs except student creation
        registrationBean.addUrlPatterns("/*");
        
        // Set the order (lower numbers have higher priority)
        registrationBean.setOrder(1);
        
        // Optional: Set name for the filter
        registrationBean.setName("BasicAuthFilter");
        
        return registrationBean;
    }
}
