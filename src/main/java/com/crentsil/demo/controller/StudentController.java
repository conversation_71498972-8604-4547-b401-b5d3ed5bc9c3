package com.crentsil.demo.controller;

import com.crentsil.demo.dto.StudentCreateDto;
import com.crentsil.demo.dto.StudentResponseDto;
import com.crentsil.demo.dto.StudentSearchDto;
import com.crentsil.demo.dto.StudentUpdateDto;
import com.crentsil.demo.mapper.StudentMapper;
import com.crentsil.demo.model.Student;
import com.crentsil.demo.repository.StudentRepository;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/students")
public class StudentController {
    private final StudentRepository studentRepository;
    private final StudentMapper studentMapper;

    @Autowired
    public StudentController(StudentRepository studentRepository, StudentMapper studentMapper) {
        this.studentRepository = studentRepository;
        this.studentMapper = studentMapper;
    }

    @PostMapping("/create")
    public ResponseEntity<?> addStudent(@Valid @RequestBody StudentCreateDto studentCreateDto, BindingResult bindingResult) {
        // Check for validation errors
        if (bindingResult.hasErrors()) {
            StringBuilder errorMessage = new StringBuilder("Validation errors: ");
            bindingResult.getFieldErrors().forEach(error ->
                errorMessage.append(error.getField()).append(" - ").append(error.getDefaultMessage()).append("; ")
            );
            return ResponseEntity.badRequest().body(errorMessage.toString());
        }

        try {
            Student student = studentMapper.toEntity(studentCreateDto);
            Student savedStudent = studentRepository.save(student);
            StudentResponseDto responseDto = studentMapper.toResponseDto(savedStudent);
            System.out.println("Adding student: " + savedStudent);
            return ResponseEntity.ok(responseDto);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error creating student: " + e.getMessage());
        }
    }

    @PostMapping("/search")
    public ResponseEntity<?> findStudentsByName(@Valid @RequestBody StudentSearchDto studentSearchDto, BindingResult bindingResult) {
        // Check for validation errors
        if (bindingResult.hasErrors()) {
            StringBuilder errorMessage = new StringBuilder("Validation errors: ");
            bindingResult.getFieldErrors().forEach(error ->
                errorMessage.append(error.getField()).append(" - ").append(error.getDefaultMessage()).append("; ")
            );
            return ResponseEntity.badRequest().body(errorMessage.toString());
        }

        // At least one search criteria must be provided
        if ((studentSearchDto.getName() == null || studentSearchDto.getName().trim().isEmpty())
            && studentSearchDto.getMinAge() == null && studentSearchDto.getMaxAge() == null) {
            return ResponseEntity.badRequest().body("At least one search criteria must be provided");
        }

        try {
            List<Student> foundStudents;

            if (studentSearchDto.getName() != null && !studentSearchDto.getName().trim().isEmpty()) {
                System.out.println("Searching for students with name: " + studentSearchDto.getName());
                foundStudents = studentRepository.findByNameContainingIgnoreCase(studentSearchDto.getName().trim());
            } else {
                // For now, if no name is provided, return all students (you can enhance this with age filtering)
                foundStudents = studentRepository.findAll();
            }

            if (foundStudents.isEmpty()) {
                return ResponseEntity.ok().body("No students found matching the criteria");
            }

            List<StudentResponseDto> responseDtos = studentMapper.toResponseDtoList(foundStudents);
            return ResponseEntity.ok(responseDtos);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error searching students: " + e.getMessage());
        }
    }

    @GetMapping
    public ResponseEntity<List<StudentResponseDto>> getAllStudents() {
        List<Student> students = studentRepository.findAll();
        List<StudentResponseDto> responseDtos = studentMapper.toResponseDtoList(students);
        return ResponseEntity.ok(responseDtos);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getStudentById(@PathVariable Long id) {
        Optional<Student> studentOpt = studentRepository.findById(id);
        if (studentOpt.isEmpty()) {
            return ResponseEntity.badRequest().body("Student with ID " + id + " not found");
        }

        StudentResponseDto responseDto = studentMapper.toResponseDto(studentOpt.get());
        return ResponseEntity.ok(responseDto);
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateStudent(@PathVariable Long id, @Valid @RequestBody StudentUpdateDto studentUpdateDto, BindingResult bindingResult) {
        // Check for validation errors
        if (bindingResult.hasErrors()) {
            StringBuilder errorMessage = new StringBuilder("Validation errors: ");
            bindingResult.getFieldErrors().forEach(error ->
                errorMessage.append(error.getField()).append(" - ").append(error.getDefaultMessage()).append("; ")
            );
            return ResponseEntity.badRequest().body(errorMessage.toString());
        }

        Optional<Student> studentOpt = studentRepository.findById(id);
        if (studentOpt.isEmpty()) {
            return ResponseEntity.badRequest().body("Student with ID " + id + " not found");
        }

        try {
            Student student = studentOpt.get();
            studentMapper.updateEntityFromDto(student, studentUpdateDto);
            Student updatedStudent = studentRepository.save(student);
            StudentResponseDto responseDto = studentMapper.toResponseDto(updatedStudent);
            return ResponseEntity.ok(responseDto);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error updating student: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteStudent(@PathVariable Long id) {
        if (!studentRepository.existsById(id)) {
            return ResponseEntity.badRequest().body("Student with ID " + id + " not found");
        }

        try {
            studentRepository.deleteById(id);
            return ResponseEntity.ok().body("Student with ID " + id + " deleted successfully");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error deleting student: " + e.getMessage());
        }
    }
}
