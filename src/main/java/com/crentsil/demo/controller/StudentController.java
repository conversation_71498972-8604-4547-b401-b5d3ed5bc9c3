package com.crentsil.demo.controller;

import com.crentsil.demo.model.Course;
import com.crentsil.demo.model.Student;
import com.crentsil.demo.repository.StudentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/students")
public class StudentController {
    private final StudentRepository studentRepository;

    @Autowired
    public StudentController(StudentRepository studentRepository) {
        this.studentRepository = studentRepository;
    }

    @PostMapping("create")
    public ResponseEntity<?> addStudent(@RequestBody Student student) {
        if (student.getName().isEmpty()){
            return ResponseEntity.badRequest().body("name should be mandetory");
        }
        Student studentBody = studentRepository.save(student);
        System.out.println("Adding student: " + studentBody);
        return ResponseEntity.ok(studentBody);
    }

@PostMapping("search")
//public ResponseEntity<List<Student>> findStudentsByName(@RequestBody Student student) {
    public ResponseEntity<?> findStudentsByName(@RequestBody Student student) {
        if (student.getName() == null || student.getName().trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        System.out.println("Searching for students with name: " + student.getName());
        List<Student> foundStudents = studentRepository.findByNameContainingIgnoreCase(student.getName());

        if (foundStudents.isEmpty()) {
            return ResponseEntity.badRequest().body("no students with that name");
        }

        return ResponseEntity.ok(foundStudents);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteStudent(@PathVariable Long id) {
        if (!studentRepository.existsById(id)) {
            return ResponseEntity.badRequest().body("Student with ID " + id + " not found");
        }

        try {
            studentRepository.deleteById(id);
            return ResponseEntity.ok().body("Student with ID " + id + " deleted successfully");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error deleting student: " + e.getMessage());
        }
    }
}
