package com.crentsil.demo.controller;

import com.crentsil.demo.dto.StudentCreateDto;
import com.crentsil.demo.dto.StudentResponseDto;
import com.crentsil.demo.dto.StudentSearchDto;
import com.crentsil.demo.dto.StudentUpdateDto;
import com.crentsil.demo.service.StudentService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/students")
public class StudentController {
    private final StudentService studentService;

    @Autowired
    public StudentController(StudentService studentService) {
        this.studentService = studentService;
    }

    @PostMapping("/create")
    public ResponseEntity<StudentResponseDto> addStudent(@Valid @RequestBody StudentCreateDto studentCreateDto, BindingResult bindingResult) {
        StudentResponseDto responseDto = studentService.createStudent(studentCreateDto, bindingResult);
        return ResponseEntity.ok(responseDto);
    }

    @PostMapping("/search")
    public ResponseEntity<?> findStudentsByName(@Valid @RequestBody StudentSearchDto studentSearchDto, BindingResult bindingResult) {
        List<StudentResponseDto> responseDtos = studentService.searchStudents(studentSearchDto, bindingResult);

        if (responseDtos.isEmpty()) {
            return ResponseEntity.ok().body("No students found matching the criteria");
        }

        return ResponseEntity.ok(responseDtos);
    }

    @GetMapping
    public ResponseEntity<List<StudentResponseDto>> getAllStudents() {
        List<StudentResponseDto> responseDtos = studentService.getAllStudents();
        return ResponseEntity.ok(responseDtos);
    }

    @GetMapping("/{id}")
    public ResponseEntity<StudentResponseDto> getStudentById(@PathVariable Long id) {
        StudentResponseDto responseDto = studentService.getStudentById(id);
        return ResponseEntity.ok(responseDto);
    }

    @PutMapping("/{id}")
    public ResponseEntity<StudentResponseDto> updateStudent(@PathVariable Long id, @Valid @RequestBody StudentUpdateDto studentUpdateDto, BindingResult bindingResult) {
        StudentResponseDto responseDto = studentService.updateStudent(id, studentUpdateDto, bindingResult);
        return ResponseEntity.ok(responseDto);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteStudent(@PathVariable Long id) {
        studentService.deleteStudent(id);
        return ResponseEntity.ok().body("Student with ID " + id + " deleted successfully");
    }
}
