package com.crentsil.demo.controller;

import com.crentsil.demo.model.Course;
import com.crentsil.demo.model.Student;
import com.crentsil.demo.repository.CourseRepository;
import com.crentsil.demo.repository.StudentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/enrollment")
public class EnrollmentController {

    private final StudentRepository studentRepository;
    private final CourseRepository courseRepository;

    @Autowired
    public EnrollmentController(StudentRepository studentRepository, CourseRepository courseRepository) {
        this.studentRepository = studentRepository;
        this.courseRepository = courseRepository;
    }

    @GetMapping("/enroll/{studentId}/{courseId}")
    public ResponseEntity<?> enrollStudentInCourse(@PathVariable Long studentId, @PathVariable Long courseId) {
        Optional<Student> studentOpt = studentRepository.findById(studentId);
        Optional<Course> courseOpt = courseRepository.findById(courseId);

        if (studentOpt.isEmpty()) {
            return ResponseEntity.badRequest().body("Student with ID " + studentId + " not found");
        }
        if (courseOpt.isEmpty()) {
            return ResponseEntity.badRequest().body("Course with ID " + courseId + " not found");
        }

        Student student = studentOpt.get();
        Course course = courseOpt.get();

        if (student.getCourses().contains(course)) {
            return ResponseEntity.badRequest().body("Student is already enrolled in this course");
        }

        student.getCourses().add(course);
        course.getStudents().add(student);

        studentRepository.save(student);

        return ResponseEntity.ok().body("Student " + student.getName() + " enrolled in course " + course.getCourseName());
    }

    @DeleteMapping("/unenroll/{studentId}/{courseId}")
    public ResponseEntity<?> unenrollStudentFromCourse(@PathVariable Long studentId, @PathVariable Long courseId) {
        Optional<Student> studentOpt = studentRepository.findById(studentId);
        Optional<Course> courseOpt = courseRepository.findById(courseId);

        if (studentOpt.isEmpty()) {
            return ResponseEntity.badRequest().body("Student with ID " + studentId + " not found");
        }
        if (courseOpt.isEmpty()) {
            return ResponseEntity.badRequest().body("Course with ID " + courseId + " not found");
        }

        Student student = studentOpt.get();
        Course course = courseOpt.get();

        if (!student.getCourses().contains(course)) {
            return ResponseEntity.badRequest().body("Student is not enrolled in this course");
        }

        student.getCourses().remove(course);
        course.getStudents().remove(student);

        studentRepository.save(student);

        return ResponseEntity.ok().body("Student " + student.getName() + " unenrolled from course " + course.getCourseName());
    }

    @GetMapping("/student/{studentId}/courses")
    public ResponseEntity<?> getStudentCourses(@PathVariable Long studentId) {
        Optional<Student> studentOpt = studentRepository.findById(studentId);
        if (studentOpt.isEmpty()) {
            return ResponseEntity.badRequest().body("Student with ID " + studentId + " not found");
        }

        List<Course> courses = courseRepository.findCoursesByStudentId(studentId);
        return ResponseEntity.ok(courses);
    }

    @GetMapping("/course/{courseId}/students")
    public ResponseEntity<?> getCourseStudents(@PathVariable Long courseId) {
        Optional<Course> courseOpt = courseRepository.findById(courseId);
        if (courseOpt.isEmpty()) {
            return ResponseEntity.badRequest().body("Course with ID " + courseId + " not found");
        }

        Course course = courseOpt.get();
        return ResponseEntity.ok(course.getStudents());
    }
}
