package com.crentsil.demo.controller;

import com.crentsil.demo.dto.CourseResponseDto;
import com.crentsil.demo.dto.StudentResponseDto;
import com.crentsil.demo.service.EnrollmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/enrollment")
public class EnrollmentController {

    private final EnrollmentService enrollmentService;

    @Autowired
    public EnrollmentController(EnrollmentService enrollmentService) {
        this.enrollmentService = enrollmentService;
    }

    @GetMapping("/enroll/{studentId}/{courseId}")
    public ResponseEntity<String> enrollStudentInCourse(@PathVariable Long studentId, @PathVariable Long courseId) {
        String message = enrollmentService.enrollStudentInCourse(studentId, courseId);
        return ResponseEntity.ok().body(message);
    }

    @DeleteMapping("/unenroll/{studentId}/{courseId}")
    public ResponseEntity<String> unenrollStudentFromCourse(@PathVariable Long studentId, @PathVariable Long courseId) {
        String message = enrollmentService.unenrollStudentFromCourse(studentId, courseId);
        return ResponseEntity.ok().body(message);
    }

    @GetMapping("/student/{studentId}/courses")
    public ResponseEntity<List<CourseResponseDto>> getStudentCourses(@PathVariable Long studentId) {
        List<CourseResponseDto> courses = enrollmentService.getStudentCourses(studentId);
        return ResponseEntity.ok(courses);
    }

    @GetMapping("/course/{courseId}/students")
    public ResponseEntity<List<StudentResponseDto>> getCourseStudents(@PathVariable Long courseId) {
        List<StudentResponseDto> students = enrollmentService.getCourseStudents(courseId);
        return ResponseEntity.ok(students);
    }
}
