package com.crentsil.demo.controller;

import com.crentsil.demo.dto.CourseResponseDto;
import com.crentsil.demo.dto.StudentResponseDto;
import com.crentsil.demo.service.EnrollmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/enrollment")
public class EnrollmentController {

    private final EnrollmentService enrollmentService;

    @Autowired
    public EnrollmentController(EnrollmentService enrollmentService) {
        this.enrollmentService = enrollmentService;
    }

    @GetMapping("/enroll/{studentId}/{courseId}")
    public ResponseEntity<?> enrollStudentInCourse(@PathVariable Long studentId, @PathVariable Long courseId) {
        try {
            String message = enrollmentService.enrollStudentInCourse(studentId, courseId);
            return ResponseEntity.ok().body(message);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error enrolling student: " + e.getMessage());
        }
    }

    @DeleteMapping("/unenroll/{studentId}/{courseId}")
    public ResponseEntity<?> unenrollStudentFromCourse(@PathVariable Long studentId, @PathVariable Long courseId) {
        try {
            String message = enrollmentService.unenrollStudentFromCourse(studentId, courseId);
            return ResponseEntity.ok().body(message);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error unenrolling student: " + e.getMessage());
        }
    }

    @GetMapping("/student/{studentId}/courses")
    public ResponseEntity<?> getStudentCourses(@PathVariable Long studentId) {
        try {
            List<CourseResponseDto> courses = enrollmentService.getStudentCourses(studentId);
            return ResponseEntity.ok(courses);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving student courses: " + e.getMessage());
        }
    }

    @GetMapping("/course/{courseId}/students")
    public ResponseEntity<?> getCourseStudents(@PathVariable Long courseId) {
        try {
            List<StudentResponseDto> students = enrollmentService.getCourseStudents(courseId);
            return ResponseEntity.ok(students);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving course students: " + e.getMessage());
        }
    }
}
