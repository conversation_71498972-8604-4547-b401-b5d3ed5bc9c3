package com.crentsil.demo.controller;

import com.crentsil.demo.dto.CourseCreateDto;
import com.crentsil.demo.dto.CourseResponseDto;
import com.crentsil.demo.dto.CourseSearchDto;
import com.crentsil.demo.mapper.CourseMapper;
import com.crentsil.demo.model.Course;
import com.crentsil.demo.repository.CourseRepository;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/courses")
public class CourseController {

    private final CourseRepository courseRepository;
    private final CourseMapper courseMapper;

    @Autowired
    public CourseController(CourseRepository courseRepository, CourseMapper courseMapper) {
        this.courseRepository = courseRepository;
        this.courseMapper = courseMapper;
    }

    @PostMapping("/create")
    public ResponseEntity<?> createCourse(@Valid @RequestBody CourseCreateDto courseCreateDto, BindingResult bindingResult) {
        // Check for validation errors
        if (bindingResult.hasErrors()) {
            StringBuilder errorMessage = new StringBuilder("Validation errors: ");
            bindingResult.getFieldErrors().forEach(error ->
                errorMessage.append(error.getField()).append(" - ").append(error.getDefaultMessage()).append("; ")
            );
            return ResponseEntity.badRequest().body(errorMessage.toString());
        }

        System.out.println("course code: " + courseCreateDto.getCourseCode());
        System.out.println("course name: " + courseCreateDto.getCourseName());
        System.out.println("course des: " + courseCreateDto.getDescription());
        System.out.println("course credit: " + courseCreateDto.getCredits());

        // Check if course code already exists
        if (courseRepository.existsByCourseCode(courseCreateDto.getCourseCode())) {
            return ResponseEntity.badRequest().body("Course with code " + courseCreateDto.getCourseCode() + " already exists");
        }

        try {
            Course course = courseMapper.toEntity(courseCreateDto);
            Course savedCourse = courseRepository.save(course);
            CourseResponseDto responseDto = courseMapper.toResponseDto(savedCourse);
            System.out.println("Created course: " + savedCourse);
            return ResponseEntity.ok(responseDto);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error creating course: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteCourse(@PathVariable Long id) {
        if (!courseRepository.existsById(id)) {
            return ResponseEntity.badRequest().body("Course with ID " + id + " not found");
        }
        
        try {
            courseRepository.deleteById(id);
            return ResponseEntity.ok().body("Course with ID " + id + " deleted successfully");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error deleting course: " + e.getMessage());
        }
    }

    @GetMapping
    public ResponseEntity<List<CourseResponseDto>> getAllCourses() {
        List<Course> courses = courseRepository.findAll();
        List<CourseResponseDto> responseDtos = courseMapper.toResponseDtoList(courses);
        return ResponseEntity.ok(responseDtos);
    }

//    @GetMapping("/{id}")
//    public ResponseEntity<?> getCourseById(@PathVariable Long id) {
//        return courseRepository.findById(id)
//                .map(course -> ResponseEntity.ok().body(course))
//                .orElse(ResponseEntity.badRequest().body("Course with ID " + id + " not found"));
//    }

    @PostMapping("/search")
    public ResponseEntity<?> searchCourses(@Valid @RequestBody CourseSearchDto courseSearchDto, BindingResult bindingResult) {
        // Check for validation errors
        if (bindingResult.hasErrors()) {
            StringBuilder errorMessage = new StringBuilder("Validation errors: ");
            bindingResult.getFieldErrors().forEach(error ->
                errorMessage.append(error.getField()).append(" - ").append(error.getDefaultMessage()).append("; ")
            );
            return ResponseEntity.badRequest().body(errorMessage.toString());
        }

        String searchName = courseSearchDto.getCourseName().trim();
        System.out.println("Searching for courses with name containing: '" + searchName + "'");

        List<Course> foundCourses = courseRepository.findByCourseNameContainingIgnoreCase(searchName);
        System.out.println("Found " + foundCourses.size() + " courses matching search");

        if (foundCourses.isEmpty()) {
            return ResponseEntity.ok().body("No courses found with name containing: " + searchName);
        }

        List<CourseResponseDto> responseDtos = courseMapper.toResponseDtoList(foundCourses);
        return ResponseEntity.ok(responseDtos);
    }
}
