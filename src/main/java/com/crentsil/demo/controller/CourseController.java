package com.crentsil.demo.controller;

import com.crentsil.demo.model.Course;
import com.crentsil.demo.repository.CourseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/courses")
public class CourseController {
    
    private final CourseRepository courseRepository;

    @Autowired
    public CourseController(CourseRepository courseRepository) {
        this.courseRepository = courseRepository;
    }

    @PostMapping("/create")
    public ResponseEntity<?> createCourse(@RequestBody Course course) {
        System.out.println("course code: " + course.getCourseCode());
        System.out.println("course name: " + course.getCourseName());
        System.out.println("course des: " + course.getDescription());
        System.out.println("course credit: " + course.getCredits());

        // Validate required fields
        if (course.getCourseCode() == null || course.getCourseCode().trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Course code is mandatory");
        }
        if (course.getCourseName() == null || course.getCourseName().trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Course name is mandatory");
        }
        if (course.getDescription() == null || course.getDescription().trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Course description is mandatory");
        }
        if (course.getCredits() == null || course.getCredits() <= 0) {
            return ResponseEntity.badRequest().body("Credits must be a positive number");
        }

        // Check if course code already exists
        if (courseRepository.existsByCourseCode(course.getCourseCode())) {
            return ResponseEntity.badRequest().body("Course with code " + course.getCourseCode() + " already exists");
        }

        try {
            Course savedCourse = courseRepository.save(course);
            System.out.println("Created course: " + savedCourse);
            return ResponseEntity.ok(savedCourse);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error creating course: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteCourse(@PathVariable Long id) {
        if (!courseRepository.existsById(id)) {
            return ResponseEntity.badRequest().body("Course with ID " + id + " not found");
        }
        
        try {
            courseRepository.deleteById(id);
            return ResponseEntity.ok().body("Course with ID " + id + " deleted successfully");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error deleting course: " + e.getMessage());
        }
    }

    @GetMapping
    public ResponseEntity<List<Course>> getAllCourses() {
        List<Course> courses = courseRepository.findAll();
        return ResponseEntity.ok(courses);
    }

//    @GetMapping("/{id}")
//    public ResponseEntity<?> getCourseById(@PathVariable Long id) {
//        return courseRepository.findById(id)
//                .map(course -> ResponseEntity.ok().body(course))
//                .orElse(ResponseEntity.badRequest().body("Course with ID " + id + " not found"));
//    }

    @PostMapping("/search")
    public ResponseEntity<?> searchCourses(@RequestBody Course course) {
        if (course.getCourseName() == null || course.getCourseName().trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Course name for search cannot be empty");
        }

        String searchName = course.getCourseName().trim();
        System.out.println("Searching for courses with name containing: '" + searchName + "'");
        
        List<Course> foundCourses = courseRepository.findByCourseNameContainingIgnoreCase(searchName);
        System.out.println("Found " + foundCourses.size() + " courses matching search");

        if (foundCourses.isEmpty()) {
            return ResponseEntity.ok().body("No courses found with name containing: " + searchName);
        }

        return ResponseEntity.ok(foundCourses);
    }
}
