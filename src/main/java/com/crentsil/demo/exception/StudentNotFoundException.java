package com.crentsil.demo.exception;

public class StudentNotFoundException extends RuntimeException {
    
    public StudentNotFoundException(String message) {
        super(message);
    }
    
    public StudentNotFoundException(Long studentId) {
        super("Student with ID " + studentId + " not found");
    }
    
    public StudentNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
