package com.crentsil.demo.exception;

import org.springframework.validation.BindingResult;

public class ValidationException extends RuntimeException {
    
    private final BindingResult bindingResult;
    
    public ValidationException(String message) {
        super(message);
        this.bindingResult = null;
    }
    
    public ValidationException(BindingResult bindingResult) {
        super(buildErrorMessage(bindingResult));
        this.bindingResult = bindingResult;
    }
    
    public ValidationException(String message, BindingResult bindingResult) {
        super(message);
        this.bindingResult = bindingResult;
    }
    
    public BindingResult getBindingResult() {
        return bindingResult;
    }
    
    private static String buildErrorMessage(BindingResult bindingResult) {
        StringBuilder errorMessage = new StringBuilder("Validation errors: ");
        bindingResult.getFieldErrors().forEach(error -> 
            errorMessage.append(error.getField()).append(" - ").append(error.getDefaultMessage()).append("; ")
        );
        return errorMessage.toString();
    }
}
