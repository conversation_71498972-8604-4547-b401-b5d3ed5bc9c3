package com.crentsil.demo.service;

import com.crentsil.demo.dto.StudentCreateDto;
import com.crentsil.demo.dto.StudentResponseDto;
import com.crentsil.demo.dto.StudentSearchDto;
import com.crentsil.demo.dto.StudentUpdateDto;
import com.crentsil.demo.exception.StudentNotFoundException;
import com.crentsil.demo.exception.ValidationException;
import com.crentsil.demo.mapper.StudentMapper;
import com.crentsil.demo.model.Student;
import com.crentsil.demo.repository.StudentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.BindingResult;

import java.util.List;
import java.util.Optional;

@Service
public class StudentService {
    
    private final StudentRepository studentRepository;
    private final StudentMapper studentMapper;
    
    @Autowired
    public StudentService(StudentRepository studentRepository, StudentMapper studentMapper) {
        this.studentRepository = studentRepository;
        this.studentMapper = studentMapper;
    }
    
    public StudentResponseDto createStudent(StudentCreateDto studentCreateDto, BindingResult bindingResult) {
        // Check for validation errors
        if (bindingResult.hasErrors()) {
            throw new ValidationException(bindingResult);
        }

        Student student = studentMapper.toEntity(studentCreateDto);
        Student savedStudent = studentRepository.save(student);
        System.out.println("Adding student: " + savedStudent);

        return studentMapper.toResponseDto(savedStudent);
    }
    
    public List<StudentResponseDto> getAllStudents() {
        List<Student> students = studentRepository.findAll();
        return studentMapper.toResponseDtoList(students);
    }
    
    public StudentResponseDto getStudentById(Long id) {
        Student student = studentRepository.findById(id)
                .orElseThrow(() -> new StudentNotFoundException(id));
        return studentMapper.toResponseDto(student);
    }
    
    public StudentResponseDto updateStudent(Long id, StudentUpdateDto studentUpdateDto, BindingResult bindingResult) {
        // Check for validation errors
        if (bindingResult.hasErrors()) {
            throw new ValidationException(bindingResult);
        }

        Student student = studentRepository.findById(id)
                .orElseThrow(() -> new StudentNotFoundException(id));

        studentMapper.updateEntityFromDto(student, studentUpdateDto);
        Student updatedStudent = studentRepository.save(student);

        return studentMapper.toResponseDto(updatedStudent);
    }
    
    public List<StudentResponseDto> searchStudents(StudentSearchDto studentSearchDto, BindingResult bindingResult) {
        // Check for validation errors
        if (bindingResult.hasErrors()) {
            throw new ValidationException(bindingResult);
        }

        // At least one search criteria must be provided
        if ((studentSearchDto.getName() == null || studentSearchDto.getName().trim().isEmpty())
            && studentSearchDto.getMinAge() == null && studentSearchDto.getMaxAge() == null) {
            throw new IllegalArgumentException("At least one search criteria must be provided");
        }

        List<Student> foundStudents;

        if (studentSearchDto.getName() != null && !studentSearchDto.getName().trim().isEmpty()) {
            System.out.println("Searching for students with name: " + studentSearchDto.getName());
            foundStudents = studentRepository.findByNameContainingIgnoreCase(studentSearchDto.getName().trim());
        } else {
            // For now, if no name is provided, return all students (you can enhance this with age filtering)
            foundStudents = studentRepository.findAll();
        }

        return studentMapper.toResponseDtoList(foundStudents);
    }
    
    public void deleteStudent(Long id) {
        if (!studentRepository.existsById(id)) {
            throw new StudentNotFoundException(id);
        }
        studentRepository.deleteById(id);
    }
    
    public boolean studentExists(Long id) {
        return studentRepository.existsById(id);
    }
    
    public Optional<Student> findStudentEntityById(Long id) {
        return studentRepository.findById(id);
    }
}
