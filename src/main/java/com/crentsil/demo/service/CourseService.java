package com.crentsil.demo.service;

import com.crentsil.demo.dto.CourseCreateDto;
import com.crentsil.demo.dto.CourseResponseDto;
import com.crentsil.demo.dto.CourseSearchDto;
import com.crentsil.demo.exception.CourseNotFoundException;
import com.crentsil.demo.mapper.CourseMapper;
import com.crentsil.demo.model.Course;
import com.crentsil.demo.repository.CourseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class CourseService {
    
    private final CourseRepository courseRepository;
    private final CourseMapper courseMapper;
    
    @Autowired
    public CourseService(CourseRepository courseRepository, CourseMapper courseMapper) {
        this.courseRepository = courseRepository;
        this.courseMapper = courseMapper;
    }
    
    public CourseResponseDto createCourse(CourseCreateDto courseCreateDto) {
        System.out.println("course code: " + courseCreateDto.getCourseCode());
        System.out.println("course name: " + courseCreateDto.getCourseName());
        System.out.println("course des: " + courseCreateDto.getDescription());
        System.out.println("course credit: " + courseCreateDto.getCredits());

        // Check if course code already exists
        if (courseRepository.existsByCourseCode(courseCreateDto.getCourseCode())) {
            throw new IllegalArgumentException("Course with code " + courseCreateDto.getCourseCode() + " already exists");
        }

        Course course = courseMapper.toEntity(courseCreateDto);
        Course savedCourse = courseRepository.save(course);
        System.out.println("Created course: " + savedCourse);
        
        return courseMapper.toResponseDto(savedCourse);
    }
    
    public List<CourseResponseDto> getAllCourses() {
        List<Course> courses = courseRepository.findAll();
        return courseMapper.toResponseDtoList(courses);
    }
    
    public CourseResponseDto getCourseById(Long id) {
        Course course = courseRepository.findById(id)
                .orElseThrow(() -> new CourseNotFoundException(id));
        return courseMapper.toResponseDto(course);
    }
    
    public List<CourseResponseDto> searchCourses(CourseSearchDto courseSearchDto) {
        String searchName = courseSearchDto.getCourseName().trim();
        System.out.println("Searching for courses with name containing: '" + searchName + "'");
        
        List<Course> foundCourses = courseRepository.findByCourseNameContainingIgnoreCase(searchName);
        System.out.println("Found " + foundCourses.size() + " courses matching search");
        
        return courseMapper.toResponseDtoList(foundCourses);
    }
    
    public void deleteCourse(Long id) {
        if (!courseRepository.existsById(id)) {
            throw new CourseNotFoundException(id);
        }
        courseRepository.deleteById(id);
    }
    
    public boolean courseExists(Long id) {
        return courseRepository.existsById(id);
    }
    
    public Optional<Course> findCourseEntityById(Long id) {
        return courseRepository.findById(id);
    }
}
