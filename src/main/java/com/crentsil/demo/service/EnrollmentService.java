package com.crentsil.demo.service;

import com.crentsil.demo.dto.CourseResponseDto;
import com.crentsil.demo.dto.StudentResponseDto;
import com.crentsil.demo.exception.CourseNotFoundException;
import com.crentsil.demo.exception.StudentNotFoundException;
import com.crentsil.demo.mapper.CourseMapper;
import com.crentsil.demo.mapper.StudentMapper;
import com.crentsil.demo.model.Course;
import com.crentsil.demo.model.Student;
import com.crentsil.demo.repository.CourseRepository;
import com.crentsil.demo.repository.StudentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
public class EnrollmentService {
    
    private final StudentRepository studentRepository;
    private final CourseRepository courseRepository;
    private final StudentMapper studentMapper;
    private final CourseMapper courseMapper;
    
    @Autowired
    public EnrollmentService(StudentRepository studentRepository, CourseRepository courseRepository,
                           StudentMapper studentMapper, CourseMapper courseMapper) {
        this.studentRepository = studentRepository;
        this.courseRepository = courseRepository;
        this.studentMapper = studentMapper;
        this.courseMapper = courseMapper;
    }
    
    public String enrollStudentInCourse(Long studentId, Long courseId) {
        Student student = studentRepository.findById(studentId)
                .orElseThrow(() -> new StudentNotFoundException(studentId));
        Course course = courseRepository.findById(courseId)
                .orElseThrow(() -> new CourseNotFoundException(courseId));

        if (student.getCourses().contains(course)) {
            throw new IllegalArgumentException("Student is already enrolled in this course");
        }

        student.getCourses().add(course);
        course.getStudents().add(student);

        studentRepository.save(student);

        return "Student " + student.getName() + " enrolled in course " + course.getCourseName();
    }
    
    public String unenrollStudentFromCourse(Long studentId, Long courseId) {
        Student student = studentRepository.findById(studentId)
                .orElseThrow(() -> new StudentNotFoundException(studentId));
        Course course = courseRepository.findById(courseId)
                .orElseThrow(() -> new CourseNotFoundException(courseId));

        if (!student.getCourses().contains(course)) {
            throw new IllegalArgumentException("Student is not enrolled in this course");
        }

        student.getCourses().remove(course);
        course.getStudents().remove(student);

        studentRepository.save(student);

        return "Student " + student.getName() + " unenrolled from course " + course.getCourseName();
    }
    
    public List<CourseResponseDto> getStudentCourses(Long studentId) {
        // Verify student exists
        studentRepository.findById(studentId)
                .orElseThrow(() -> new StudentNotFoundException(studentId));

        List<Course> courses = courseRepository.findCoursesByStudentId(studentId);
        return courseMapper.toResponseDtoList(courses);
    }
    
    public List<StudentResponseDto> getCourseStudents(Long courseId) {
        Course course = courseRepository.findById(courseId)
                .orElseThrow(() -> new CourseNotFoundException(courseId));

        Set<Student> students = course.getStudents();
        return studentMapper.toResponseDtoList(students.stream().toList());
    }
}
