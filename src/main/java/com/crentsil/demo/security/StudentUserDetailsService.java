package com.crentsil.demo.security;

import com.crentsil.demo.model.Student;
import com.crentsil.demo.repository.StudentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class StudentUserDetailsService implements UserDetailsService {

    private final StudentRepository studentRepository;

    @Autowired
    public StudentUserDetailsService(StudentRepository studentRepository) {
        this.studentRepository = studentRepository;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // Find student by name (username)
        List<Student> students = studentRepository.findByName(username);
        
        if (students.isEmpty()) {
            throw new UsernameNotFoundException("Student not found with name: " + username);
        }
        
        // If multiple students have the same name, use the first one
        Student student = students.get(0);
        
        // Create UserDetails with student's name and password
        // Note: In production, passwords should be encoded
        return User.builder()
                .username(student.getName())
                .password("{noop}" + student.getPassword()) // {noop} means no password encoding
                .authorities(new ArrayList<>()) // No specific authorities for now
                .build();
    }
}
