package com.crentsil.demo.dto;

import jakarta.validation.constraints.*;
import lombok.Data;

@Data
public class CourseCreateDto {
    
    @NotBlank(message = "Course code is mandatory")
    @Size(min = 2, max = 20, message = "Course code must be between 2 and 20 characters")
    @Pattern(regexp = "^[A-Z0-9]+$", message = "Course code must contain only uppercase letters and numbers")
    private String courseCode;
    
    @NotBlank(message = "Course name is mandatory")
    @Size(min = 3, max = 255, message = "Course name must be between 3 and 255 characters")
    private String courseName;
    
    @NotBlank(message = "Course description is mandatory")
    @Size(min = 10, max = 1000, message = "Course description must be between 10 and 1000 characters")
    private String description;
    
    @NotNull(message = "Credits is mandatory")
    @Min(value = 1, message = "Credits must be at least 1")
    @Max(value = 100, message = "Credits cannot exceed 10")
    private Integer credits;
}
