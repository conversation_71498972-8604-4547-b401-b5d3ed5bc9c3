package com.crentsil.demo.dto;

import jakarta.validation.constraints.*;
import lombok.Data;

@Data
public class StudentCreateDto {
    
    @NotBlank(message = "Student name is mandatory")
    @Size(min = 2, max = 100, message = "Student name must be between 2 and 100 characters")
    @Pattern(regexp = "^[a-zA-Z\\s]+$", message = "Student name must contain only letters and spaces")
    private String name;
    
    @NotNull(message = "Age is mandatory")
    @Min(value = 16, message = "Student must be at least 16 years old")
    @Max(value = 100, message = "Age cannot exceed 100 years")
    private Integer age;
    
    @NotBlank(message = "Date of birth is mandatory")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "Date of birth must be in format yyyy-mm-dd")
    private String dob;
    
    @NotBlank(message = "Password is mandatory")
    @Size(min = 6, max = 50, message = "Password must be between 6 and 50 characters")
    private String password;
}
