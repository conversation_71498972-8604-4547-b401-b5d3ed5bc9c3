package com.crentsil.demo.middleware;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;

import jakarta.servlet.Filter;
import java.io.IOException;
import java.util.Base64;

//import java.util.logging.Filter;
@Component
public class BasicAuth implements Filter {
    @Override
    public  void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) req;
        HttpServletResponse httpRes = (HttpServletResponse) res;
        String authHeader = httpRequest.getHeader("Authorization");

        if (authHeader == null || !authHeader.startsWith("Basic "))
        {
            httpRes.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            httpRes.setHeader("Auth failed","invalid login attempt");
            httpRes.getWriter().write("wrong details");
            return ;
        }
        String credentails = authHeader.substring("Basic ".length());
        String model = new String(Base64.getDecoder().decode(credentails));
        String[] part = model.split(":",2);
        if (part.length != 2){
            httpRes.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            httpRes.setHeader("Auth failed","invalid login attempt");
            httpRes.getWriter().write("wrong details");
            return ;
        }
          String username = part[0];
          String password = part[1];   


    }
}
