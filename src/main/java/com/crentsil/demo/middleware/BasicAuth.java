package com.crentsil.demo.middleware;
import com.crentsil.demo.model.Student;
import com.crentsil.demo.repository.StudentRepository;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.servlet.Filter;
import java.io.IOException;
import java.util.Base64;
import java.util.List;

//import java.util.logging.Filter;
@Component
public class BasicAuth implements Filter {

    @Autowired
    private StudentRepository studentRepository;
    @Override
    public  void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) req;
        HttpServletResponse httpRes = (HttpServletResponse) res;

        // Skip authentication for student creation endpoint
        String requestURI = httpRequest.getRequestURI();
        if (requestURI.equals("/students/create")) {
            chain.doFilter(req, res);
            return;
        }

        String authHeader = httpRequest.getHeader("Authorization");

        if (authHeader == null || !authHeader.startsWith("Basic "))
        {
            httpRes.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            httpRes.setHeader("Auth failed","invalid login attempt");
            httpRes.getWriter().write("wrong details");
            return ;
        }
        String credentails = authHeader.substring("Basic ".length());
        String model = new String(Base64.getDecoder().decode(credentails));
        String[] part = model.split(":",2);
        if (part.length != 2){
            httpRes.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            httpRes.setHeader("Auth failed","invalid login attempt");
            httpRes.getWriter().write("wrong details");
            return ;
        }
          String username = part[0];
          String password = part[1];

        // Check if username and password exist in student table
        List<Student> students = studentRepository.findByName(username);

        if (students.isEmpty()) {
            httpRes.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            httpRes.setHeader("Auth failed", "Student not found");
            httpRes.getWriter().write("Authentication failed: Student not found");
            return;
        }

        // Get the first student with matching name
        Student student = students.get(0);

        // Check if password matches
        if (!password.equals(student.getPassword())) {
            httpRes.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            httpRes.setHeader("Auth failed", "Invalid password");
            httpRes.getWriter().write("Authentication failed: Invalid password");
            return;
        }

        // Authentication successful, continue with the request
        chain.doFilter(req, res);
    }
}
