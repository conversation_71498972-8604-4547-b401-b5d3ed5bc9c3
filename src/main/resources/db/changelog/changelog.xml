<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="2" author="curtis">
        <validCheckSum>9:5dafe1f1fc611996f250fa63873602e0</validCheckSum>
        <createTable tableName="student">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="age" type="INT">
                <constraints nullable="true"/>
            </column>
            <column name="dob" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="password" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="3" author="curtis">
        <createTable tableName="course">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="course_code" type="VARCHAR(20)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="course_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="credits" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="4" author="curtis">
        <createTable tableName="course_enrollment">
            <column name="student_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="course_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addPrimaryKey tableName="course_enrollment"
                       columnNames="student_id, course_id"
                       constraintName="pk_course_enrollment"/>

        <addForeignKeyConstraint baseTableName="course_enrollment"
                                 baseColumnNames="student_id"
                                 referencedTableName="student"
                                 referencedColumnNames="id"
                                 constraintName="fk_enrollment_student"
                                 onDelete="CASCADE"/>

        <addForeignKeyConstraint baseTableName="course_enrollment"
                                 baseColumnNames="course_id"
                                 referencedTableName="course"
                                 referencedColumnNames="id"
                                 constraintName="fk_enrollment_course"
                                 onDelete="CASCADE"/>
    </changeSet>
</databaseChangeLog>