package com.crentsil.demo.security;

import com.crentsil.demo.dto.CourseCreateDto;
import com.crentsil.demo.dto.StudentCreateDto;
import com.crentsil.demo.model.Student;
import com.crentsil.demo.repository.StudentRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.Base64;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureTestMvc
@ActiveProfiles("test")
@Transactional
public class BasicAuthenticationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private StudentRepository studentRepository;

    private Student testStudent;

    @BeforeEach
    void setUp() {
        // Create a test student for authentication
        testStudent = new Student();
        testStudent.setName("John Doe");
        testStudent.setAge(20);
        testStudent.setDob("2003-01-15");
        testStudent.setPassword("password123");
        testStudent = studentRepository.save(testStudent);
    }

    @Test
    public void testStudentCreation_NoAuthRequired_Success() throws Exception {
        // Given - Student creation data
        StudentCreateDto studentDto = new StudentCreateDto();
        studentDto.setName("Jane Smith");
        studentDto.setAge(22);
        studentDto.setDob("2001-05-10");
        studentDto.setPassword("mypassword");

        // When & Then - Should succeed without authentication
        mockMvc.perform(post("/students/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(studentDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Jane Smith"));
    }

    @Test
    public void testGetAllStudents_NoAuth_Unauthorized() throws Exception {
        // When & Then - Should return 401 Unauthorized
        mockMvc.perform(get("/students"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    public void testGetAllStudents_WithValidAuth_Success() throws Exception {
        // Given - Valid Basic Auth credentials
        String credentials = testStudent.getName() + ":" + testStudent.getPassword();
        String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());

        // When & Then - Should succeed with valid authentication
        mockMvc.perform(get("/students")
                .header("Authorization", "Basic " + encodedCredentials))
                .andExpect(status().isOk());
    }

    @Test
    public void testGetAllStudents_WithInvalidAuth_Unauthorized() throws Exception {
        // Given - Invalid Basic Auth credentials
        String credentials = "wronguser:wrongpassword";
        String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());

        // When & Then - Should return 401 Unauthorized
        mockMvc.perform(get("/students")
                .header("Authorization", "Basic " + encodedCredentials))
                .andExpect(status().isUnauthorized());
    }

    @Test
    public void testCourseCreation_NoAuth_Unauthorized() throws Exception {
        // Given - Course creation data
        CourseCreateDto courseDto = new CourseCreateDto();
        courseDto.setCourseCode("CS101");
        courseDto.setCourseName("Introduction to Computer Science");
        courseDto.setDescription("Basic computer science concepts and programming fundamentals");
        courseDto.setCredits(3);

        // When & Then - Should return 401 Unauthorized
        mockMvc.perform(post("/courses/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(courseDto)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    public void testCourseCreation_WithValidAuth_Success() throws Exception {
        // Given - Valid Basic Auth credentials and course data
        String credentials = testStudent.getName() + ":" + testStudent.getPassword();
        String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());

        CourseCreateDto courseDto = new CourseCreateDto();
        courseDto.setCourseCode("CS101");
        courseDto.setCourseName("Introduction to Computer Science");
        courseDto.setDescription("Basic computer science concepts and programming fundamentals");
        courseDto.setCredits(3);

        // When & Then - Should succeed with valid authentication
        mockMvc.perform(post("/courses/create")
                .header("Authorization", "Basic " + encodedCredentials)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(courseDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.courseCode").value("CS101"));
    }

    @Test
    public void testEnrollment_WithValidAuth_Success() throws Exception {
        // Given - Valid Basic Auth credentials
        String credentials = testStudent.getName() + ":" + testStudent.getPassword();
        String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());

        // First create a course
        CourseCreateDto courseDto = new CourseCreateDto();
        courseDto.setCourseCode("MATH101");
        courseDto.setCourseName("Calculus I");
        courseDto.setDescription("Introduction to calculus and mathematical analysis");
        courseDto.setCredits(4);

        String courseResponse = mockMvc.perform(post("/courses/create")
                .header("Authorization", "Basic " + encodedCredentials)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(courseDto)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        // Extract course ID from response (simplified - in real scenario you'd parse JSON)
        // For this test, we'll assume the course gets ID 1
        Long courseId = 1L;

        // When & Then - Should succeed with valid authentication
        mockMvc.perform(get("/enrollment/enroll/" + testStudent.getId() + "/" + courseId)
                .header("Authorization", "Basic " + encodedCredentials))
                .andExpect(status().isOk());
    }
}
