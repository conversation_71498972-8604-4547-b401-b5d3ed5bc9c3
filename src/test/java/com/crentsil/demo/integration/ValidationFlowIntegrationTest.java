package com.crentsil.demo.integration;

import com.crentsil.demo.dto.CourseCreateDto;
import com.crentsil.demo.dto.StudentCreateDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureTestMvc
@ActiveProfiles("test")
@Transactional
public class ValidationFlowIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testCourseCreation_ValidationError_HandledByGlobalExceptionHandler() throws Exception {
        // Given - Invalid course data
        CourseCreateDto invalidCourse = new CourseCreateDto();
        invalidCourse.setCourseCode("cs"); // Invalid: lowercase and too short
        invalidCourse.setCourseName(""); // Invalid: empty
        invalidCourse.setDescription("short"); // Invalid: too short
        invalidCourse.setCredits(0); // Invalid: must be at least 1

        // When & Then - Should return 400 with validation error message
        mockMvc.perform(post("/courses/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidCourse)))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Validation errors")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("courseCode")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("courseName")));
    }

    @Test
    public void testStudentCreation_ValidationError_HandledByGlobalExceptionHandler() throws Exception {
        // Given - Invalid student data
        StudentCreateDto invalidStudent = new StudentCreateDto();
        invalidStudent.setName("A"); // Invalid: too short
        invalidStudent.setAge(15); // Invalid: too young
        invalidStudent.setDob("invalid-date"); // Invalid: wrong format
        invalidStudent.setPassword("123"); // Invalid: too short

        // When & Then - Should return 400 with validation error message
        mockMvc.perform(post("/students/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidStudent)))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Validation errors")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("name")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("age")));
    }

    @Test
    public void testCourseCreation_ValidData_Success() throws Exception {
        // Given - Valid course data
        CourseCreateDto validCourse = new CourseCreateDto();
        validCourse.setCourseCode("CS101");
        validCourse.setCourseName("Introduction to Computer Science");
        validCourse.setDescription("Basic computer science concepts and programming fundamentals");
        validCourse.setCredits(3);

        // When & Then - Should return 200 with course data
        mockMvc.perform(post("/courses/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validCourse)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.courseCode").value("CS101"))
                .andExpect(jsonPath("$.courseName").value("Introduction to Computer Science"))
                .andExpect(jsonPath("$.credits").value(3));
    }

    @Test
    public void testStudentCreation_ValidData_Success() throws Exception {
        // Given - Valid student data
        StudentCreateDto validStudent = new StudentCreateDto();
        validStudent.setName("John Doe");
        validStudent.setAge(20);
        validStudent.setDob("2003-01-15");
        validStudent.setPassword("password123");

        // When & Then - Should return 200 with student data
        mockMvc.perform(post("/students/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validStudent)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("John Doe"))
                .andExpect(jsonPath("$.age").value(20))
                .andExpect(jsonPath("$.dob").value("2003-01-15"));
    }
}
