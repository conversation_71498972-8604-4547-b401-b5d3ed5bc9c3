package com.crentsil.demo.integration;

import com.crentsil.demo.dto.StudentCreateDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.Base64;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureTestMvc
@ActiveProfiles("test")
@Transactional
public class AuthenticationIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testCompleteAuthenticationFlow() throws Exception {
        // Step 1: Create a student account (no auth required)
        StudentCreateDto studentDto = new StudentCreateDto();
        studentDto.setName("Alice Johnson");
        studentDto.setAge(21);
        studentDto.setDob("2002-03-20");
        studentDto.setPassword("securepassword");

        mockMvc.perform(post("/students/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(studentDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Alice Johnson"));

        // Step 2: Try to access protected endpoint without auth (should fail)
        mockMvc.perform(get("/students"))
                .andExpect(status().isUnauthorized());

        // Step 3: Access protected endpoint with valid credentials (should succeed)
        String credentials = "Alice Johnson:securepassword";
        String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());

        mockMvc.perform(get("/students")
                .header("Authorization", "Basic " + encodedCredentials))
                .andExpect(status().isOk());

        // Step 4: Test auth status endpoint
        mockMvc.perform(get("/auth/status")
                .header("Authorization", "Basic " + encodedCredentials))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.authenticated").value(true))
                .andExpect(jsonPath("$.username").value("Alice Johnson"));

        // Step 5: Test auth test endpoint
        mockMvc.perform(get("/auth/test")
                .header("Authorization", "Basic " + encodedCredentials))
                .andExpect(status().isOk())
                .andExpect(content().string("Hello Alice Johnson! Authentication is working."));

        // Step 6: Try with wrong password (should fail)
        String wrongCredentials = "Alice Johnson:wrongpassword";
        String wrongEncodedCredentials = Base64.getEncoder().encodeToString(wrongCredentials.getBytes());

        mockMvc.perform(get("/students")
                .header("Authorization", "Basic " + wrongEncodedCredentials))
                .andExpect(status().isUnauthorized());

        // Step 7: Try with non-existent user (should fail)
        String nonExistentCredentials = "NonExistent User:anypassword";
        String nonExistentEncodedCredentials = Base64.getEncoder().encodeToString(nonExistentCredentials.getBytes());

        mockMvc.perform(get("/students")
                .header("Authorization", "Basic " + nonExistentEncodedCredentials))
                .andExpect(status().isUnauthorized());
    }

    @Test
    public void testAllEndpointsRequireAuthExceptStudentCreation() throws Exception {
        // Test that all endpoints return 401 without authentication
        
        // Student endpoints (except create)
        mockMvc.perform(get("/students")).andExpect(status().isUnauthorized());
        mockMvc.perform(get("/students/1")).andExpect(status().isUnauthorized());
        mockMvc.perform(put("/students/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")).andExpect(status().isUnauthorized());
        mockMvc.perform(delete("/students/1")).andExpect(status().isUnauthorized());
        mockMvc.perform(post("/students/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")).andExpect(status().isUnauthorized());

        // Course endpoints
        mockMvc.perform(post("/courses/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")).andExpect(status().isUnauthorized());
        mockMvc.perform(get("/courses")).andExpect(status().isUnauthorized());
        mockMvc.perform(get("/courses/1")).andExpect(status().isUnauthorized());
        mockMvc.perform(delete("/courses/1")).andExpect(status().isUnauthorized());
        mockMvc.perform(post("/courses/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")).andExpect(status().isUnauthorized());

        // Enrollment endpoints
        mockMvc.perform(get("/enrollment/enroll/1/1")).andExpect(status().isUnauthorized());
        mockMvc.perform(delete("/enrollment/unenroll/1/1")).andExpect(status().isUnauthorized());
        mockMvc.perform(get("/enrollment/student/1/courses")).andExpect(status().isUnauthorized());
        mockMvc.perform(get("/enrollment/course/1/students")).andExpect(status().isUnauthorized());

        // Auth endpoints
        mockMvc.perform(get("/auth/status")).andExpect(status().isUnauthorized());
        mockMvc.perform(get("/auth/test")).andExpect(status().isUnauthorized());

        // Student creation should still work without auth
        StudentCreateDto studentDto = new StudentCreateDto();
        studentDto.setName("Test User");
        studentDto.setAge(20);
        studentDto.setDob("2003-01-01");
        studentDto.setPassword("testpass");

        mockMvc.perform(post("/students/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(studentDto)))
                .andExpect(status().isOk());
    }
}
