package com.crentsil.demo.repository;

import com.crentsil.demo.model.Course;
import com.crentsil.demo.model.Student;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

@DataJpaTest
public class CourseRepositoryTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private CourseRepository courseRepository;

    @Test
    public void testFindByCourseCode() {
        // Given
        Course course = new Course();
        course.setCourseCode("CS101");
        course.setCourseName("Introduction to Computer Science");
        course.setDescription("Basic computer science concepts");
        course.setCredits(3);
        entityManager.persistAndFlush(course);

        // When
        Optional<Course> found = courseRepository.findByCourseCode("CS101");

        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getCourseName()).isEqualTo("Introduction to Computer Science");
    }

    @Test
    public void testFindByCourseNameContainingIgnoreCase() {
        // Given
        Course course1 = new Course();
        course1.setCourseCode("CS101");
        course1.setCourseName("Introduction to Computer Science");
        course1.setDescription("Basic computer science concepts");
        course1.setCredits(3);
        entityManager.persistAndFlush(course1);

        Course course2 = new Course();
        course2.setCourseCode("CS201");
        course2.setCourseName("Advanced Computer Programming");
        course2.setDescription("Advanced programming concepts");
        course2.setCredits(4);
        entityManager.persistAndFlush(course2);

        // When & Then
        List<Course> foundCourses = courseRepository.findByCourseNameContainingIgnoreCase("computer");
        assertThat(foundCourses).hasSize(2);

        foundCourses = courseRepository.findByCourseNameContainingIgnoreCase("ADVANCED");
        assertThat(foundCourses).hasSize(1);
        assertThat(foundCourses.get(0).getCourseCode()).isEqualTo("CS201");
    }

    @Test
    public void testExistsByCourseCode() {
        // Given
        Course course = new Course();
        course.setCourseCode("CS101");
        course.setCourseName("Introduction to Computer Science");
        course.setDescription("Basic computer science concepts");
        course.setCredits(3);
        entityManager.persistAndFlush(course);

        // When & Then
        assertThat(courseRepository.existsByCourseCode("CS101")).isTrue();
        assertThat(courseRepository.existsByCourseCode("CS999")).isFalse();
    }

    @Test
    public void testFindCoursesByStudentId() {
        // Given
        Student student = new Student();
        student.setName("John Doe");
        student.setAge(20);
        student.setDob("2003-01-01");
        student.setPassword("password123");
        entityManager.persistAndFlush(student);

        Course course1 = new Course();
        course1.setCourseCode("CS101");
        course1.setCourseName("Introduction to Computer Science");
        course1.setDescription("Basic computer science concepts");
        course1.setCredits(3);
        entityManager.persistAndFlush(course1);

        Course course2 = new Course();
        course2.setCourseCode("MATH101");
        course2.setCourseName("Calculus I");
        course2.setDescription("Introduction to calculus");
        course2.setCredits(4);
        entityManager.persistAndFlush(course2);

        // Establish relationships
        student.getCourses().add(course1);
        student.getCourses().add(course2);
        course1.getStudents().add(student);
        course2.getStudents().add(student);
        entityManager.persistAndFlush(student);

        // When
        List<Course> studentCourses = courseRepository.findCoursesByStudentId(student.getId());

        // Then
        assertThat(studentCourses).hasSize(2);
        assertThat(studentCourses).extracting(Course::getCourseCode)
                .containsExactlyInAnyOrder("CS101", "MATH101");
    }
}
