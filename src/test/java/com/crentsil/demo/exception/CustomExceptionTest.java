package com.crentsil.demo.exception;

import com.crentsil.demo.service.CourseService;
import com.crentsil.demo.service.StudentService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureTestMvc
@ActiveProfiles("test")
@Transactional
public class CustomExceptionTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private StudentService studentService;

    @Autowired
    private CourseService courseService;

    @Test
    public void testStudentNotFoundException_ThrownByService() {
        // Test that service throws StudentNotFoundException for non-existent student
        assertThrows(StudentNotFoundException.class, () -> {
            studentService.getStudentById(999L);
        });
    }

    @Test
    public void testCourseNotFoundException_ThrownByService() {
        // Test that service throws CourseNotFoundException for non-existent course
        assertThrows(CourseNotFoundException.class, () -> {
            courseService.getCourseById(999L);
        });
    }

    @Test
    public void testStudentNotFoundException_HandledByGlobalExceptionHandler() throws Exception {
        // Test that global exception handler returns 404 for StudentNotFoundException
        mockMvc.perform(get("/students/999"))
                .andExpect(status().isNotFound())
                .andExpect(content().string("Student with ID 999 not found"));
    }

    @Test
    public void testCourseNotFoundException_HandledByGlobalExceptionHandler() throws Exception {
        // Test that global exception handler returns 404 for CourseNotFoundException
        mockMvc.perform(get("/courses/999"))
                .andExpect(status().isNotFound())
                .andExpect(content().string("Course with ID 999 not found"));
    }

    @Test
    public void testEnrollmentWithNonExistentStudent() throws Exception {
        // Test enrollment with non-existent student returns 404
        mockMvc.perform(get("/enrollment/enroll/999/1"))
                .andExpect(status().isNotFound())
                .andExpect(content().string("Student with ID 999 not found"));
    }

    @Test
    public void testEnrollmentWithNonExistentCourse() throws Exception {
        // Test enrollment with non-existent course returns 404
        mockMvc.perform(get("/enrollment/enroll/1/999"))
                .andExpect(status().isNotFound())
                .andExpect(content().string("Course with ID 999 not found"));
    }
}
