package com.crentsil.demo.controller;

import com.crentsil.demo.dto.StudentCreateDto;
import com.crentsil.demo.dto.StudentSearchDto;
import com.crentsil.demo.dto.StudentUpdateDto;
import com.crentsil.demo.repository.StudentRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureTestMvc
@ActiveProfiles("test")
@Transactional
public class StudentControllerValidationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private StudentRepository studentRepository;

    @Test
    public void testCreateStudent_ValidData_Success() throws Exception {
        StudentCreateDto studentDto = new StudentCreateDto();
        studentDto.setName("John Doe");
        studentDto.setAge(20);
        studentDto.setDob("2003-01-15");
        studentDto.setPassword("password123");

        mockMvc.perform(post("/students/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(studentDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("John Doe"))
                .andExpect(jsonPath("$.age").value(20))
                .andExpect(jsonPath("$.dob").value("2003-01-15"));
    }

    @Test
    public void testCreateStudent_InvalidName_ValidationError() throws Exception {
        StudentCreateDto studentDto = new StudentCreateDto();
        studentDto.setName("J"); // Too short
        studentDto.setAge(20);
        studentDto.setDob("2003-01-15");
        studentDto.setPassword("password123");

        mockMvc.perform(post("/students/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(studentDto)))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Student name must be between 2 and 100 characters")));
    }

    @Test
    public void testCreateStudent_InvalidAge_ValidationError() throws Exception {
        StudentCreateDto studentDto = new StudentCreateDto();
        studentDto.setName("John Doe");
        studentDto.setAge(15); // Too young
        studentDto.setDob("2008-01-15");
        studentDto.setPassword("password123");

        mockMvc.perform(post("/students/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(studentDto)))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Student must be at least 16 years old")));
    }

    @Test
    public void testCreateStudent_InvalidDateFormat_ValidationError() throws Exception {
        StudentCreateDto studentDto = new StudentCreateDto();
        studentDto.setName("John Doe");
        studentDto.setAge(20);
        studentDto.setDob("01/15/2003"); // Wrong format
        studentDto.setPassword("password123");

        mockMvc.perform(post("/students/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(studentDto)))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Date of birth must be in format yyyy-mm-dd")));
    }

    @Test
    public void testCreateStudent_ShortPassword_ValidationError() throws Exception {
        StudentCreateDto studentDto = new StudentCreateDto();
        studentDto.setName("John Doe");
        studentDto.setAge(20);
        studentDto.setDob("2003-01-15");
        studentDto.setPassword("123"); // Too short

        mockMvc.perform(post("/students/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(studentDto)))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Password must be between 6 and 50 characters")));
    }
}
