package com.crentsil.demo.controller;

import com.crentsil.demo.dto.CourseCreateDto;
import com.crentsil.demo.repository.CourseRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureTestMvc
@ActiveProfiles("test")
@Transactional
public class CourseControllerValidationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CourseRepository courseRepository;

    @Test
    public void testCreateCourse_ValidData_Success() throws Exception {
        CourseCreateDto courseDto = new CourseCreateDto();
        courseDto.setCourseCode("CS101");
        courseDto.setCourseName("Introduction to Computer Science");
        courseDto.setDescription("Basic computer science concepts and programming fundamentals");
        courseDto.setCredits(3);

        mockMvc.perform(post("/courses/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(courseDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.courseCode").value("CS101"))
                .andExpect(jsonPath("$.courseName").value("Introduction to Computer Science"))
                .andExpect(jsonPath("$.credits").value(3));
    }

    @Test
    public void testCreateCourse_InvalidCourseCode_ValidationError() throws Exception {
        CourseCreateDto courseDto = new CourseCreateDto();
        courseDto.setCourseCode("cs"); // Invalid: too short and lowercase
        courseDto.setCourseName("Introduction to Computer Science");
        courseDto.setDescription("Basic computer science concepts and programming fundamentals");
        courseDto.setCredits(3);

        mockMvc.perform(post("/courses/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(courseDto)))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Validation errors")));
    }

    @Test
    public void testCreateCourse_MissingCourseName_ValidationError() throws Exception {
        CourseCreateDto courseDto = new CourseCreateDto();
        courseDto.setCourseCode("CS101");
        // Missing courseName
        courseDto.setDescription("Basic computer science concepts and programming fundamentals");
        courseDto.setCredits(3);

        mockMvc.perform(post("/courses/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(courseDto)))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Course name is mandatory")));
    }

    @Test
    public void testCreateCourse_InvalidCredits_ValidationError() throws Exception {
        CourseCreateDto courseDto = new CourseCreateDto();
        courseDto.setCourseCode("CS101");
        courseDto.setCourseName("Introduction to Computer Science");
        courseDto.setDescription("Basic computer science concepts and programming fundamentals");
        courseDto.setCredits(0); // Invalid: must be at least 1

        mockMvc.perform(post("/courses/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(courseDto)))
                .andExpect(status().isBadRequest())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Credits must be at least 1")));
    }
}
