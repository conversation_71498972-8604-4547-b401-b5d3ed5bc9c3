package com.crentsil.demo.service;

import com.crentsil.demo.dto.CourseCreateDto;
import com.crentsil.demo.dto.CourseResponseDto;
import com.crentsil.demo.mapper.CourseMapper;
import com.crentsil.demo.model.Course;
import com.crentsil.demo.repository.CourseRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CourseServiceTest {

    @Mock
    private CourseRepository courseRepository;

    @Mock
    private CourseMapper courseMapper;

    @InjectMocks
    private CourseService courseService;

    private CourseCreateDto courseCreateDto;
    private Course course;
    private CourseResponseDto courseResponseDto;

    @BeforeEach
    void setUp() {
        courseCreateDto = new CourseCreateDto();
        courseCreateDto.setCourseCode("CS101");
        courseCreateDto.setCourseName("Introduction to Computer Science");
        courseCreateDto.setDescription("Basic computer science concepts");
        courseCreateDto.setCredits(3);

        course = new Course();
        course.setId(1L);
        course.setCourseCode("CS101");
        course.setCourseName("Introduction to Computer Science");
        course.setDescription("Basic computer science concepts");
        course.setCredits(3);

        courseResponseDto = new CourseResponseDto();
        courseResponseDto.setId(1L);
        courseResponseDto.setCourseCode("CS101");
        courseResponseDto.setCourseName("Introduction to Computer Science");
        courseResponseDto.setDescription("Basic computer science concepts");
        courseResponseDto.setCredits(3);
    }

    @Test
    void testCreateCourse_Success() {
        // Given
        when(courseRepository.existsByCourseCode("CS101")).thenReturn(false);
        when(courseMapper.toEntity(courseCreateDto)).thenReturn(course);
        when(courseRepository.save(course)).thenReturn(course);
        when(courseMapper.toResponseDto(course)).thenReturn(courseResponseDto);

        // When
        CourseResponseDto result = courseService.createCourse(courseCreateDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getCourseCode()).isEqualTo("CS101");
        assertThat(result.getCourseName()).isEqualTo("Introduction to Computer Science");
        verify(courseRepository).existsByCourseCode("CS101");
        verify(courseRepository).save(course);
    }

    @Test
    void testCreateCourse_CourseCodeAlreadyExists() {
        // Given
        when(courseRepository.existsByCourseCode("CS101")).thenReturn(true);

        // When & Then
        assertThatThrownBy(() -> courseService.createCourse(courseCreateDto))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Course with code CS101 already exists");

        verify(courseRepository).existsByCourseCode("CS101");
        verify(courseRepository, never()).save(any());
    }

    @Test
    void testGetAllCourses() {
        // Given
        List<Course> courses = Arrays.asList(course);
        List<CourseResponseDto> responseDtos = Arrays.asList(courseResponseDto);
        
        when(courseRepository.findAll()).thenReturn(courses);
        when(courseMapper.toResponseDtoList(courses)).thenReturn(responseDtos);

        // When
        List<CourseResponseDto> result = courseService.getAllCourses();

        // Then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getCourseCode()).isEqualTo("CS101");
        verify(courseRepository).findAll();
    }
}
