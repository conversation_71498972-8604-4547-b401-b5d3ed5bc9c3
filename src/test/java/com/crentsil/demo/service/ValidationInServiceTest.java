package com.crentsil.demo.service;

import com.crentsil.demo.dto.CourseCreateDto;
import com.crentsil.demo.dto.StudentCreateDto;
import com.crentsil.demo.exception.ValidationException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.Validator;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ValidationInServiceTest {

    @Autowired
    private CourseService courseService;

    @Autowired
    private StudentService studentService;

    @Autowired
    private Validator validator;

    @Test
    public void testCourseService_ValidationError_ThrowsValidationException() {
        // Given - Invalid course data
        CourseCreateDto invalidCourse = new CourseCreateDto();
        invalidCourse.setCourseCode(""); // Invalid: empty
        invalidCourse.setCourseName(""); // Invalid: empty
        invalidCourse.setDescription(""); // Invalid: empty
        invalidCourse.setCredits(0); // Invalid: must be at least 1

        // Create BindingResult with validation errors
        BindingResult bindingResult = new BeanPropertyBindingResult(invalidCourse, "courseCreateDto");
        validator.validate(invalidCourse, bindingResult);

        // When & Then - Service should throw ValidationException
        ValidationException exception = assertThrows(ValidationException.class, () -> {
            courseService.createCourse(invalidCourse, bindingResult);
        });

        // Verify the exception message contains validation errors
        assertTrue(exception.getMessage().contains("Validation errors"));
        assertTrue(exception.getMessage().contains("courseCode"));
        assertTrue(exception.getMessage().contains("courseName"));
    }

    @Test
    public void testStudentService_ValidationError_ThrowsValidationException() {
        // Given - Invalid student data
        StudentCreateDto invalidStudent = new StudentCreateDto();
        invalidStudent.setName("A"); // Invalid: too short
        invalidStudent.setAge(15); // Invalid: too young
        invalidStudent.setDob("invalid-date"); // Invalid: wrong format
        invalidStudent.setPassword("123"); // Invalid: too short

        // Create BindingResult with validation errors
        BindingResult bindingResult = new BeanPropertyBindingResult(invalidStudent, "studentCreateDto");
        validator.validate(invalidStudent, bindingResult);

        // When & Then - Service should throw ValidationException
        ValidationException exception = assertThrows(ValidationException.class, () -> {
            studentService.createStudent(invalidStudent, bindingResult);
        });

        // Verify the exception message contains validation errors
        assertTrue(exception.getMessage().contains("Validation errors"));
        assertTrue(exception.getMessage().contains("name"));
        assertTrue(exception.getMessage().contains("age"));
    }

    @Test
    public void testCourseService_ValidData_NoValidationException() {
        // Given - Valid course data
        CourseCreateDto validCourse = new CourseCreateDto();
        validCourse.setCourseCode("CS101");
        validCourse.setCourseName("Introduction to Computer Science");
        validCourse.setDescription("Basic computer science concepts and programming fundamentals");
        validCourse.setCredits(3);

        // Create BindingResult with no validation errors
        BindingResult bindingResult = new BeanPropertyBindingResult(validCourse, "courseCreateDto");
        validator.validate(validCourse, bindingResult);

        // When & Then - Service should not throw ValidationException
        assertDoesNotThrow(() -> {
            courseService.createCourse(validCourse, bindingResult);
        });
    }

    @Test
    public void testStudentService_ValidData_NoValidationException() {
        // Given - Valid student data
        StudentCreateDto validStudent = new StudentCreateDto();
        validStudent.setName("John Doe");
        validStudent.setAge(20);
        validStudent.setDob("2003-01-15");
        validStudent.setPassword("password123");

        // Create BindingResult with no validation errors
        BindingResult bindingResult = new BeanPropertyBindingResult(validStudent, "studentCreateDto");
        validator.validate(validStudent, bindingResult);

        // When & Then - Service should not throw ValidationException
        assertDoesNotThrow(() -> {
            studentService.createStudent(validStudent, bindingResult);
        });
    }
}
