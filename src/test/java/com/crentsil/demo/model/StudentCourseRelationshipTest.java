package com.crentsil.demo.model;

import com.crentsil.demo.repository.CourseRepository;
import com.crentsil.demo.repository.StudentRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@DataJpaTest
public class StudentCourseRelationshipTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private StudentRepository studentRepository;

    @Autowired
    private CourseRepository courseRepository;

    @Test
    public void testManyToManyRelationship() {
        // Given
        Student student1 = new Student();
        student1.setName("John Doe");
        student1.setAge(20);
        student1.setDob("2003-01-01");
        student1.setPassword("password123");

        Student student2 = new Student();
        student2.setName("<PERSON>");
        student2.setAge(22);
        student2.setDob("2001-05-15");
        student2.setPassword("password456");

        Course course1 = new Course();
        course1.setCourseCode("CS101");
        course1.setCourseName("Introduction to Computer Science");
        course1.setDescription("Basic computer science concepts");
        course1.setCredits(3);

        Course course2 = new Course();
        course2.setCourseCode("MATH101");
        course2.setCourseName("Calculus I");
        course2.setDescription("Introduction to calculus");
        course2.setCredits(4);

        // When - Establish relationships
        student1.getCourses().add(course1);
        student1.getCourses().add(course2);
        student2.getCourses().add(course1);

        course1.getStudents().add(student1);
        course1.getStudents().add(student2);
        course2.getStudents().add(student1);

        // Save entities
        entityManager.persistAndFlush(student1);
        entityManager.persistAndFlush(student2);
        entityManager.persistAndFlush(course1);
        entityManager.persistAndFlush(course2);

        // Then - Verify relationships
        Student savedStudent1 = studentRepository.findById(student1.getId()).orElse(null);
        assertThat(savedStudent1).isNotNull();
        assertThat(savedStudent1.getCourses()).hasSize(2);

        Student savedStudent2 = studentRepository.findById(student2.getId()).orElse(null);
        assertThat(savedStudent2).isNotNull();
        assertThat(savedStudent2.getCourses()).hasSize(1);

        Course savedCourse1 = courseRepository.findById(course1.getId()).orElse(null);
        assertThat(savedCourse1).isNotNull();
        assertThat(savedCourse1.getStudents()).hasSize(2);

        Course savedCourse2 = courseRepository.findById(course2.getId()).orElse(null);
        assertThat(savedCourse2).isNotNull();
        assertThat(savedCourse2.getStudents()).hasSize(1);
    }

    @Test
    public void testCascadeOperations() {
        // Given
        Student student = new Student();
        student.setName("John Doe");
        student.setAge(20);
        student.setDob("2003-01-01");
        student.setPassword("password123");

        Course course = new Course();
        course.setCourseCode("CS101");
        course.setCourseName("Introduction to Computer Science");
        course.setDescription("Basic computer science concepts");
        course.setCredits(3);

        // When - Add course to student and save student (should cascade to course)
        student.getCourses().add(course);
        course.getStudents().add(student);

        Student savedStudent = studentRepository.save(student);

        // Then
        assertThat(savedStudent.getCourses()).hasSize(1);
        
        // Verify course was also saved due to cascade
        List<Course> allCourses = courseRepository.findAll();
        assertThat(allCourses).hasSize(1);
        assertThat(allCourses.get(0).getCourseCode()).isEqualTo("CS101");
    }

    @Test
    public void testFindCoursesByStudentId() {
        // Given
        Student student = new Student();
        student.setName("John Doe");
        student.setAge(20);
        student.setDob("2003-01-01");
        student.setPassword("password123");
        entityManager.persistAndFlush(student);

        Course course1 = new Course();
        course1.setCourseCode("CS101");
        course1.setCourseName("Introduction to Computer Science");
        course1.setDescription("Basic computer science concepts");
        course1.setCredits(3);

        Course course2 = new Course();
        course2.setCourseCode("MATH101");
        course2.setCourseName("Calculus I");
        course2.setDescription("Introduction to calculus");
        course2.setCredits(4);

        student.getCourses().add(course1);
        student.getCourses().add(course2);
        course1.getStudents().add(student);
        course2.getStudents().add(student);

        entityManager.persistAndFlush(student);
        entityManager.persistAndFlush(course1);
        entityManager.persistAndFlush(course2);

        // When
        List<Course> studentCourses = courseRepository.findCoursesByStudentId(student.getId());

        // Then
        assertThat(studentCourses).hasSize(2);
        assertThat(studentCourses).extracting(Course::getCourseCode)
                .containsExactlyInAnyOrder("CS101", "MATH101");
    }
}
