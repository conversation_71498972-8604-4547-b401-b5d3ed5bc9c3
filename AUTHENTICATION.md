# Basic Authentication Guide

## Overview
The application uses HTTP Basic Authentication for all endpoints except student creation. Authentication credentials are validated against the student table using the student's name and password.

## Authentication Details

### Protected Endpoints
All endpoints require Basic Authentication **except**:
- `POST /students/create` - Public endpoint for student registration

### Authentication Credentials
- **Username**: Student's name (from the `name` field in the student table)
- **Password**: Student's password (from the `password` field in the student table)

## How to Use

### 1. Create a Student Account (No Authentication Required)
```bash
curl -X POST http://localhost:8080/students/create \
  -H "Content-Type: application/json" \
  -d '{
    "name": "<PERSON>",
    "age": 20,
    "dob": "2003-01-15",
    "password": "mypassword123"
  }'
```

### 2. Access Protected Endpoints (Authentication Required)

#### Using curl with Basic Auth:
```bash
# Get all students
curl -X GET http://localhost:8080/students \
  -u "John Doe:mypassword123"

# Create a course
curl -X POST http://localhost:8080/courses/create \
  -u "John Doe:mypassword123" \
  -H "Content-Type: application/json" \
  -d '{
    "courseCode": "CS101",
    "courseName": "Introduction to Computer Science",
    "description": "Basic computer science concepts and programming fundamentals",
    "credits": 3
  }'

# Get all courses
curl -X GET http://localhost:8080/courses \
  -u "John Doe:mypassword123"

# Enroll in a course
curl -X GET http://localhost:8080/enrollment/enroll/1/1 \
  -u "John Doe:mypassword123"
```

#### Using Postman:
1. Go to the **Authorization** tab
2. Select **Basic Auth** from the Type dropdown
3. Enter:
   - **Username**: Student's name (e.g., "John Doe")
   - **Password**: Student's password (e.g., "mypassword123")

#### Using JavaScript/Fetch:
```javascript
const credentials = btoa('John Doe:mypassword123'); // Base64 encode

fetch('http://localhost:8080/students', {
  method: 'GET',
  headers: {
    'Authorization': `Basic ${credentials}`,
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

## Protected Endpoints List

### Student Endpoints (Require Authentication)
- `GET /students` - Get all students
- `GET /students/{id}` - Get student by ID
- `PUT /students/{id}` - Update student
- `DELETE /students/{id}` - Delete student
- `POST /students/search` - Search students

### Course Endpoints (Require Authentication)
- `POST /courses/create` - Create course
- `GET /courses` - Get all courses
- `GET /courses/{id}` - Get course by ID
- `DELETE /courses/{id}` - Delete course
- `POST /courses/search` - Search courses

### Enrollment Endpoints (Require Authentication)
- `GET /enrollment/enroll/{studentId}/{courseId}` - Enroll student in course
- `DELETE /enrollment/unenroll/{studentId}/{courseId}` - Unenroll student from course
- `GET /enrollment/student/{studentId}/courses` - Get student's courses
- `GET /enrollment/course/{courseId}/students` - Get course's students

## Error Responses

### 401 Unauthorized
Returned when:
- No authentication credentials provided
- Invalid username/password combination
- Student not found in database

```json
{
  "timestamp": "2024-01-15T10:30:00.000+00:00",
  "status": 401,
  "error": "Unauthorized",
  "path": "/students"
}
```

## Security Notes

⚠️ **Important**: This implementation uses plain text passwords for simplicity. In a production environment, you should:

1. **Hash passwords** using BCrypt or similar
2. **Use HTTPS** to encrypt credentials in transit
3. **Implement proper session management**
4. **Add rate limiting** to prevent brute force attacks
5. **Consider JWT tokens** for stateless authentication

## Testing Authentication

Run the authentication tests:
```bash
mvn test -Dtest=BasicAuthenticationTest
```

This will verify that:
- Student creation works without authentication
- Protected endpoints require authentication
- Valid credentials allow access
- Invalid credentials are rejected
